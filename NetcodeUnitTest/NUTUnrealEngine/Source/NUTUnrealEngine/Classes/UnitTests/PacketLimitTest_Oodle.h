// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

// @todo #JohnB: Restore in a game-level package, eventually
#if 0

#include "CoreMinimal.h"
#include "UObject/ObjectMacros.h"
#include "UnitTests/PacketLimitTest.h"

#include "PacketLimitTest_Oodle.generated.h"

/**
 * Version of UPacketLimitTest, which tests with Oodle enabled
 */
UCLASS()
class UPacketLimitTest_Oodle : public UPacketLimitTest
{
	GENERATED_UCLASS_BODY()
};
#endif
