#include "BlueprintNodeUtility.h"

#include "EdGraphSchema_K2.h"
#include "K2Node_Event.h"
#include "K2Node_FunctionEntry.h"
#include "K2Node_VariableGet.h"
#include "K2Node_VariableSet.h"

#include "LogDruids.h"

void FBlueprintNodeUtility::SerializeNodesToContext(UBlueprint* ActiveBlueprint,
                                                    TWeakObjectPtr<UEdGraph> ActiveGraph,
                                                    const FGraphPanelSelectionSet& Nodes,
                                                    FString& OutFullContext,
                                                    FString& OutDisplayContext)
{
    if (Nodes.Num() == 0)
    {
        if (ActiveGraph.IsValid())
        {
            OutFullContext = FString::Printf(TEXT("Viewing Blueprint Graph: %s"), *ActiveGraph->GetName());
            OutDisplayContext = FString::Printf(TEXT("Graph: %s"), *ActiveGraph->GetName());
        }
        else
        {
            OutFullContext = "";
            OutDisplayContext = "";
        }
        return;
    }

    TSet<UObject*> ExpandedNodes = Nodes; //ExpandConnectedNodes(Nodes);

    // Limit the number of nodes to MaxNodeLimit
    bool bWasLimited = false;
    TSet<UObject*> FinalNodes;
    int32 NodeCount = 0;
    for (UObject* Node : ExpandedNodes)
    {
        if (NodeCount >= MaxNodeLimit)
        {
            bWasLimited = true;
            UE_LOG(LogDruidsSage, Warning, TEXT("Node limit of %d reached. Truncating additional nodes."), MaxNodeLimit);
            break;
        }
        FinalNodes.Add(Node);
        NodeCount++;
    }

    TSharedPtr<FJsonObject> JsonObject = MakeShared<FJsonObject>();
    TArray<TSharedPtr<FJsonValue>> NodeArray;
    UEdGraphNode* ProminentNode = nullptr;
    TMap<FGuid, UEdGraphNode*> NodeGuidMap;

    // First pass: Serialize node details, including variables
    for (UObject* Node : FinalNodes)
    {
        UEdGraphNode* GraphNode = Cast<UEdGraphNode>(Node);
        if (!GraphNode)
        {
            continue;
        }

        TSharedPtr<FJsonObject> NodeObject = MakeShared<FJsonObject>();
        NodeObject->SetStringField("NodeGuid", GraphNode->NodeGuid.ToString());
        NodeObject->SetStringField("NodeTitle", GraphNode->GetNodeTitle(ENodeTitleType::FullTitle).ToString());
        NodeObject->SetStringField("NodeType", GraphNode->GetClass()->GetName());

        // Add node position
        TSharedPtr<FJsonObject> PositionObject = MakeShared<FJsonObject>();
        PositionObject->SetNumberField("X", GraphNode->NodePosX);
        PositionObject->SetNumberField("Y", GraphNode->NodePosY);
        NodeObject->SetObjectField("NodePosition", PositionObject);

        // Add description (tooltip)
        NodeObject->SetStringField("Description", GraphNode->GetTooltipText().ToString());

        // Add component class for component nodes
        if (GraphNode->GetClass()->GetName().Contains(TEXT("Component")))
        {
            // Try to extract component class information
            for (UEdGraphPin* Pin : GraphNode->Pins)
            {
                if (Pin->PinType.PinCategory == TEXT("class") && Pin->PinType.PinSubCategoryObject.IsValid())
                {
                    NodeObject->SetStringField("ComponentClass", Pin->PinType.PinSubCategoryObject->GetName());
                    break;
                }
            }
        }

        // Add variable information for VariableGet and VariableSet nodes
        TArray<TSharedPtr<FJsonValue>> VariablesArray;
        if (UK2Node_VariableGet* VarGetNode = Cast<UK2Node_VariableGet>(GraphNode))
        {
            TSharedPtr<FJsonObject> VarObject = MakeShared<FJsonObject>();
            FName VarName = VarGetNode->VariableReference.GetMemberName();
            if (ActiveBlueprint)
            {
                for (const FBPVariableDescription& VarDesc : ActiveBlueprint->NewVariables)
                {
                    if (VarDesc.VarName == VarName)
                    {
                        VarObject->SetStringField("Name", VarName.ToString());
                        VarObject->SetStringField("Type", VarDesc.VarType.PinCategory.ToString());
                        VarObject->SetStringField("DefaultValue", VarDesc.DefaultValue);
                        VarObject->SetStringField("MemberGuid", VarDesc.VarGuid.ToString());
                        VarObject->SetBoolField("bSelfContext", VarGetNode->VariableReference.IsSelfContext());

                        VariablesArray.Add(MakeShared<FJsonValueObject>(VarObject));
                        break;
                    }
                }
            }
        }
        else if (UK2Node_VariableSet* VarSetNode = Cast<UK2Node_VariableSet>(GraphNode))
        {
            TSharedPtr<FJsonObject> VarObject = MakeShared<FJsonObject>();
            FName VarName = VarSetNode->VariableReference.GetMemberName();
            if (ActiveBlueprint)
            {
                for (const FBPVariableDescription& VarDesc : ActiveBlueprint->NewVariables)
                {
                    if (VarDesc.VarName == VarName)
                    {
                        VarObject->SetStringField("Name", VarName.ToString());
                        VarObject->SetStringField("Type", VarDesc.VarType.PinCategory.ToString());
                        VarObject->SetStringField("DefaultValue", VarDesc.DefaultValue);
                        VarObject->SetStringField("MemberGuid", VarDesc.VarGuid.ToString());
                        VarObject->SetBoolField("bSelfContext", VarSetNode->VariableReference.IsSelfContext());

                        if (UEdGraphPin* ValuePin = VarSetNode->FindPin(VarName))
                        {
                            if (ValuePin->LinkedTo.Num() == 0 && ValuePin->DefaultValue.Len() > 0)
                            {
                                VarObject->SetStringField("SetValue", ValuePin->DefaultValue);
                            }
                        }

                        VariablesArray.Add(MakeShared<FJsonValueObject>(VarObject));
                        break;
                    }
                }
            }
        }
        if (VariablesArray.Num() > 0)
        {
            NodeObject->SetArrayField("Variables", VariablesArray);
        }

        NodeArray.Add(MakeShared<FJsonValueObject>(NodeObject));
        NodeGuidMap.Add(GraphNode->NodeGuid, GraphNode);

        if (!ProminentNode || Cast<UK2Node_Event>(Node) || Cast<UK2Node_FunctionEntry>(Node))
        {
            ProminentNode = GraphNode;
        }
    }

    // Second pass: Add connection information
    for (int32 i = 0; i < NodeArray.Num(); ++i)
    {
        TSharedPtr<FJsonObject> NodeObject = NodeArray[i]->AsObject();
        FString NodeGuidStr;
        NodeObject->TryGetStringField(TEXT("NodeGuid"), NodeGuidStr);
        FGuid NodeGuid = FGuid(NodeGuidStr);
        UEdGraphNode* SourceNode = NodeGuidMap.FindRef(NodeGuid);

        if (!SourceNode)
            continue;

        TArray<TSharedPtr<FJsonValue>> InputsArray;
        TArray<TSharedPtr<FJsonValue>> OutputsArray;
        for (UEdGraphPin* Pin : SourceNode->Pins)
        {
            for (UEdGraphPin* LinkedPin : Pin->LinkedTo)
            {
                UEdGraphNode* TargetNode = LinkedPin->GetOwningNode();
                if (!TargetNode || !NodeGuidMap.Contains(TargetNode->NodeGuid))
                    continue;

                switch (Pin->Direction)
                {
                case EGPD_Input:
                    {
                        Pin->PinType;
                        TSharedPtr<FJsonObject> ConnectionObject = MakeShared<FJsonObject>();
                        ConnectionObject->SetStringField("ToPin", Pin->PinName.ToString());
                        ConnectionObject->SetStringField("PinType", Pin->PinType.PinCategory.ToString());
                        ConnectionObject->SetStringField("TargetGuid", TargetNode->NodeGuid.ToString());
                        ConnectionObject->SetStringField("FromPin", LinkedPin->PinName.ToString());
                        InputsArray.Add(MakeShared<FJsonValueObject>(ConnectionObject));
                    }
                    break;
                case EGPD_Output:
                    {
                        TSharedPtr<FJsonObject> ConnectionObject = MakeShared<FJsonObject>();
                        ConnectionObject->SetStringField("FromPin", Pin->PinName.ToString());
                        ConnectionObject->SetStringField("PinType", Pin->PinType.PinCategory.ToString());
                        ConnectionObject->SetStringField("TargetGuid", TargetNode->NodeGuid.ToString());
                        ConnectionObject->SetStringField("ToPin", LinkedPin->PinName.ToString());
                        OutputsArray.Add(MakeShared<FJsonValueObject>(ConnectionObject));
                    }
                    break;
                }
            }
        }
        NodeObject->SetArrayField("Input Pins", InputsArray);
        NodeObject->SetArrayField("Output Pins", OutputsArray);
    }

    // Build the full context
    JsonObject->SetArrayField("Nodes", NodeArray);
    JsonObject->SetStringField("Graph", ActiveGraph.IsValid() ? ActiveGraph->GetName() : "None");
    JsonObject->SetNumberField("NodeCount", NodeArray.Num());

    FString OutputString;
    TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> Writer =
        TJsonWriterFactory<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>::Create(&OutputString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);
    OutFullContext = OutputString;

    if (ProminentNode)
    {
        if (FinalNodes.Num() > 1)
        {
            if (bWasLimited)
            {
                OutDisplayContext = FString::Printf(TEXT("%s + %d more nodes (limited to %d)"),
                    *ProminentNode->GetNodeTitle(ENodeTitleType::MenuTitle).ToString(),
                    FinalNodes.Num() - 1, MaxNodeLimit);
            }
            else
            {
                OutDisplayContext = FString::Printf(TEXT("%s + %d more nodes"),
                    *ProminentNode->GetNodeTitle(ENodeTitleType::MenuTitle).ToString(),
                    FinalNodes.Num() - 1);
            }
        }
        else
        {
            OutDisplayContext = ProminentNode->GetNodeTitle(ENodeTitleType::MenuTitle).ToString();
        }
    }
    else
    {
        if (bWasLimited)
        {
            OutDisplayContext = FString::Printf(TEXT("%d Nodes Selected (limited to %d)"), FinalNodes.Num(), MaxNodeLimit);
        }
        else
        {
            OutDisplayContext = FString::Printf(TEXT("%d Nodes Selected"), FinalNodes.Num());
        }
    }
}

void FBlueprintNodeUtility::GatherDownstreamNodes(UEdGraphNode* StartNode, FGraphPanelSelectionSet& OutNodes)
{
    if (!StartNode)
        return;

    OutNodes.Add(StartNode);

    for (UEdGraphPin* Pin : StartNode->Pins)
    {
        if (Pin->Direction == EGPD_Output)
        {
            for (UEdGraphPin* LinkedPin : Pin->LinkedTo)
            {
                if (UEdGraphNode* LinkedNode = LinkedPin->GetOwningNode())
                {
                    if (!OutNodes.Contains(LinkedNode))
                    {
                        GatherDownstreamNodes(LinkedNode, OutNodes);
                    }
                }
            }
        }
    }
}

TSet<UObject*> FBlueprintNodeUtility::ExpandConnectedNodes(const FGraphPanelSelectionSet& InitialNodes)
{
    TSet<UObject*> ExpandedNodes;

    // Use a queue to process nodes breadth-first, ensuring we include connected nodes systematically
    TArray<UObject*> NodesToProcess;
    for (UObject* Node : InitialNodes)
    {
        NodesToProcess.Add(Node);
    }

    while (NodesToProcess.Num() > 0)
    {
        UObject* CurrentNode = NodesToProcess[0];
        NodesToProcess.RemoveAt(0);

        if (ExpandedNodes.Contains(CurrentNode))
        {
            continue; // Skip if already processed
        }

        ExpandedNodes.Add(CurrentNode);

        if (UEdGraphNode* GraphNode = Cast<UEdGraphNode>(CurrentNode))
        {
            // Add all connected nodes to the processing queue
            for (UEdGraphPin* Pin : GraphNode->Pins)
            {
                for (UEdGraphPin* LinkedPin : Pin->LinkedTo)
                {
                    UEdGraphNode* LinkedNode = LinkedPin->GetOwningNode();
                    if (!ExpandedNodes.Contains(LinkedNode))
                    {
                        NodesToProcess.Add(LinkedNode);
                    }
                }
            }
        }
    }

    return ExpandedNodes;
}

bool FBlueprintNodeUtility::AreSelectionsEqual(const FGraphPanelSelectionSet& A, const FGraphPanelSelectionSet& B)
{
    if (A.Num() != B.Num())
        return false;

    for (UObject* NodeA : A)
    {
        if (!B.Contains(NodeA))
            return false;
    }

    return true;
}