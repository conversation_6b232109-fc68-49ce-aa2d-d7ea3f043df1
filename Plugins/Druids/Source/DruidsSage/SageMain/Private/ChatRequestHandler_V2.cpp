#include "ChatRequestHandler_V2.h"
#include "DruidsSageMessagingHandler.h"
#include "DruidsSageHelper.h"
#include "IDruidsSageChatItem.h"
#include "SageExtensionDelegatorImplementation.h"

bool ChatRequestHandler_V2::IsNoActiveRequest() const
{
    return !RequestReference.IsValid() || !UDruidsSageTaskStatus::IsTaskActive(RequestReference.Get());
}

void ChatRequestHandler_V2::StopAndCleanupRequest(TArray<TSharedPtr<IDruidsSageChatItem>> ChatItems)
{
    if (RequestReference.IsValid())
    {
        // Find the last assistant message to get its messaging handler
        for (auto It = ChatItems.CreateIterator(); It; ++It)
        {
            if (const auto& ChatItem = *It)
            {
                if (ChatItem.Get()->GetMessageRole() == EDruidsSageChatRole::Assistant && ChatItem.Get()->GetMessagingHandler().IsValid())
                {
                    // Unbind all delegates
                    ChatItem.Get()->GetMessagingHandler()->OnMessageRequestSent.Unbind();
                    ChatItem.Get()->GetMessagingHandler()->OnMessageRequestFailed.Unbind();
                    ChatItem.Get()->GetMessagingHandler()->OnMessageContentUpdated.Unbind();
                    ChatItem.Get()->GetMessagingHandler()->OnMessageResponseUpdated.Unbind();
                    
                    RequestReference->ProgressStarted.RemoveAll(ChatItem.Get()->GetMessagingHandler().Get());
                    RequestReference->ProgressUpdated.RemoveAll(ChatItem.Get()->GetMessagingHandler().Get());
                    RequestReference->ProcessCompleted.RemoveAll(ChatItem.Get()->GetMessagingHandler().Get());
                    RequestReference->ErrorReceived.RemoveAll(ChatItem.Get()->GetMessagingHandler().Get());
                    RequestReference->RequestFailed.RemoveAll(ChatItem.Get()->GetMessagingHandler().Get());
                    RequestReference->RequestSent.RemoveAll(ChatItem.Get()->GetMessagingHandler().Get());
                }
            }
        }
        
        RequestReference->StopDruidsSageTask();
        RequestReference.Reset();
    }
}

void ChatRequestHandler_V2::SetupAndSendRequest(TArray<TSharedPtr<IDruidsSageChatItem>> ChatItems,
                                                 const TSharedPtr<IDruidsSageChatItem>& AssistantMessage, const FString& Context)
{
    if (!AssistantMessage.IsValid() || !AssistantMessage->GetMessagingHandler().IsValid())
    {
        return;
    }

    TArray<FDruidsSageChatMessage> ChatHistory;
	for (const TSharedPtr<IDruidsSageChatItem>& Item : ChatItems)
    {
        FString MessageRoleText = UDruidsSageHelper::RoleToName(Item->GetMessageRole()).ToString();

	    FDruidsSageChatMessage ChatMessage;
	    Item->FillInDruidsMessage(ChatMessage);
        
        // Add UserFocusContext to user messages
        if (Item->GetMessageRole() == EDruidsSageChatRole::User && !Context.IsEmpty())
        {
            ChatMessage.SetUserFocusContext(Context);
        }
        
        ChatHistory.Add(ChatMessage);
    }

    ExtensionDelegator = MakeShared<FSageExtensionDelegatorImplementation>();
    RequestReference = UDruidsSageChatRequest_v2::EditorTask(ChatHistory, ExtensionDelegator, Context);

    RequestReference->ProgressStarted.AddDynamic(
        AssistantMessage->GetMessagingHandler().Get(),
        &UDruidsSageMessagingHandler::ProcessUpdated);
        
    RequestReference->ProgressUpdated.AddDynamic(
        AssistantMessage->GetMessagingHandler().Get(),
        &UDruidsSageMessagingHandler::ProcessUpdated);
        
    RequestReference->ProcessCompleted.AddDynamic(
        AssistantMessage->GetMessagingHandler().Get(),
        &UDruidsSageMessagingHandler::ProcessCompleted);
        
    RequestReference->ErrorReceived.AddDynamic(
        AssistantMessage->GetMessagingHandler().Get(),
        &UDruidsSageMessagingHandler::ProcessCompleted);
        
    RequestReference->RequestFailed.AddDynamic(
        AssistantMessage->GetMessagingHandler().Get(),
        &UDruidsSageMessagingHandler::RequestFailed);
        
    RequestReference->RequestSent.AddDynamic(
        AssistantMessage->GetMessagingHandler().Get(),
        &UDruidsSageMessagingHandler::RequestSent);

    RequestReference->Activate();
}