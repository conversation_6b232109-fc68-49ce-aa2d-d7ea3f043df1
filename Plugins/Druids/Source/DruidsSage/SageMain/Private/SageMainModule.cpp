#include "SageMainModule.h"

#include "ChatRequestHandler_V2.h"
#include "DruidsSageEditorModule.h"
#include "SageExtensionDelegatorImplementation.h"
#include "SDruidsSageChatShell.h"
#include "UMG/DruidsChatWidgetManager.h"
#include "UMG/DruidsChatWidgetFactory.h"
#include "UMG/DruidsSageChatShell.h"
#include "Components/Widget.h"
#include "Widgets/SCompoundWidget.h"
#include "Widgets/SBoxPanel.h"
#include "LogDruids.h"

#define LOCTEXT_NAMESPACE "FSageMainModule"

void FSageMainModule::StartupModule()
{
	// Register for post-engine init callback
	FCoreDelegates::OnPostEngineInit.AddRaw(this, &FSageMainModule::OnPostEngineInit);
}

void FSageMainModule::ShutdownModule()
{
	if (FModuleManager::Get().IsModuleLoaded("DruidsSageEditorModule"))
	{
		FDruidsSageEditorModule& EditorModule = FModuleManager::GetModuleChecked<FDruidsSageEditorModule>("DruidsSageEditorModule");
		EditorModule.OnCreateChatShell.Unbind();
	}

	FCoreDelegates::OnPostEngineInit.RemoveAll(this);
}

void FSageMainModule::OnPostEngineInit()
{
	if (FModuleManager::Get().IsModuleLoaded("DruidsSageEditorModule"))
	{
		FDruidsSageEditorModule& EditorModule = FModuleManager::GetModuleChecked<FDruidsSageEditorModule>("DruidsSageEditorModule");

		// Bind a lambda to the OnCreateChatShell event
		EditorModule.OnCreateChatShell.BindLambda([this]() -> TSharedPtr<SCompoundWidget> {
			// Get widget manager from subsystem
			UDruidsChatWidgetManager* WidgetManager = UDruidsChatWidgetManager::Get(GEngine);
			if (!WidgetManager)
			{
				UE_LOG(LogDruidsSage, Error, TEXT("FSageMainModule::OnPostEngineInit - Could not get widget manager"));
				// Fall back to Slate implementation
				TSharedRef<SDruidsSageChatShell> ChatShell = SNew(SDruidsSageChatShell)
					.ChatRequestHandler(MakeShared<ChatRequestHandler_V2>())
					.ExtensionDelegator(MakeShared<FSageExtensionDelegatorImplementation>());
				return ChatShell;
			}

			UDruidsChatWidgetFactory* Factory = WidgetManager->GetWidgetFactory();
			if (!Factory)
			{
				UE_LOG(LogDruidsSage, Error, TEXT("FSageMainModule::OnPostEngineInit - Could not get widget factory"));
				// Fall back to Slate implementation
				TSharedRef<SDruidsSageChatShell> ChatShell = SNew(SDruidsSageChatShell)
					.ChatRequestHandler(MakeShared<ChatRequestHandler_V2>())
					.ExtensionDelegator(MakeShared<FSageExtensionDelegatorImplementation>());
				return ChatShell;
			}

			// Create UMG chat shell
			UDruidsSageChatShell* UMGChatShell = Factory->CreateChatShell();
			if (!UMGChatShell)
			{
				UE_LOG(LogDruidsSage, Error, TEXT("FSageMainModule::OnPostEngineInit - Could not create UMG chat shell"));
				// Fall back to Slate implementation
				TSharedRef<SDruidsSageChatShell> ChatShell = SNew(SDruidsSageChatShell)
					.ChatRequestHandler(MakeShared<ChatRequestHandler_V2>())
					.ExtensionDelegator(MakeShared<FSageExtensionDelegatorImplementation>());
				return ChatShell;
			}

			// Set up handlers using bridge system
			TSharedPtr<IChatRequestHandler> NativeChatHandler = MakeShared<ChatRequestHandler_V2>();
			TScriptInterface<IChatRequestHandler> ChatRequestHandler =
				UDruidsChatInterfaceBridgeFactory::CreateChatRequestHandlerBridge(UMGChatShell, NativeChatHandler);
			UMGChatShell->SetChatRequestHandler(ChatRequestHandler);

			TSharedPtr<ISageExtensionDelegator> NativeExtensionDelegator = MakeShared<FSageExtensionDelegatorImplementation>();
			TScriptInterface<ISageExtensionDelegator> ExtensionDelegator =
				UDruidsChatInterfaceBridgeFactory::CreateExtensionDelegatorBridge(UMGChatShell, NativeExtensionDelegator);
			UMGChatShell->SetExtensionDelegator(ExtensionDelegator);

			// Wrap UMG widget in Slate widget for editor integration
			TSharedRef<SBox> WrappedWidget = SNew(SBox)
				.HAlign(HAlign_Fill)
				.VAlign(VAlign_Fill)
				[
					UMGChatShell->TakeWidget()
				];
			return WrappedWidget;
		});
	}
}

#undef LOCTEXT_NAMESPACE
    
IMPLEMENT_MODULE(FSageMainModule, SageMain)