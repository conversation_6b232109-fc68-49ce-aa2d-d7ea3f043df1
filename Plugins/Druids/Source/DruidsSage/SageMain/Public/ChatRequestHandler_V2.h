#pragma once

#include <CoreMinimal.h>

#include "IChatRequestHandler.h"
#include "DruidsSageChatRequest_v2.h"

class ChatRequestHandler_V2 : public IChatRequestHandler
{
public:
    virtual bool IsNoActiveRequest() const override;
    virtual void StopAndCleanupRequest(TArray<TSharedPtr<IDruidsSageChatItem>> ChatItems) override;
    virtual void SetupAndSendRequest(TArray<TSharedPtr<IDruidsSageChatItem>> ChatItems,
                                     const TSharedPtr<IDruidsSageChatItem>& AssistantMessage, const FString& Context) override;

private:
    TWeakObjectPtr<UDruidsSageChatRequest_v2> RequestReference;
	TSharedPtr<ISageExtensionDelegatorImplementation> ExtensionDelegator;
};