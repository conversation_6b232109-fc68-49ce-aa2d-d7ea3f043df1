#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"

#include "IChatRequestHandler.h"
#include "ISageExtensionDelegator.h"

#include "DruidsChatInterfaces.generated.h"

/**
 * UInterface wrapper for IChatRequestHandler
 * Allows Blueprint implementation and TScriptInterface usage
 */
UINTERFACE(BlueprintType, Blueprintable)
class SAGEUI_API UDruidsChatRequestHandler : public UInterface
{
    GENERATED_BODY()
};

class SAGEUI_API IDruidsChatRequestHandler
{
    GENERATED_BODY()

public:
    // Blueprint implementable events
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Request Handler")
    void HandleChatRequest(const FString& Message, const FString& Context);

    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Request Handler")
    void HandleStreamingResponse(const FString& PartialContent);

    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Request Handler")
    void OnRequestCompleted(bool bSuccess, const FString& ErrorMessage);

    // Native interface methods (can be overridden in C++)
    virtual void HandleChatRequest_Native(const FString& Message, const FString& Context) {}
    virtual void HandleStreamingResponse_Native(const FString& PartialContent) {}
    virtual void OnRequestCompleted_Native(bool bSuccess, const FString& ErrorMessage) {}

    // Bridge to original C++ interface
    virtual IChatRequestHandler* GetNativeChatRequestHandler() const { return nullptr; }
};

/**
 * UInterface wrapper for ISageExtensionDelegator
 * Allows Blueprint implementation and TScriptInterface usage
 */
UINTERFACE(BlueprintType, Blueprintable)
class SAGEUI_API UDruidsSageExtensionDelegator : public UInterface
{
    GENERATED_BODY()
};

class SAGEUI_API IDruidsSageExtensionDelegator
{
    GENERATED_BODY()

public:
    // Blueprint implementable events
    UFUNCTION(BlueprintImplementableEvent, Category = "Sage Extension Delegator")
    void ExecuteAction(const FString& ActionName, const FString& Parameters);

    UFUNCTION(BlueprintImplementableEvent, Category = "Sage Extension Delegator")
    FString ExecuteQuery(const FString& QueryName, const FString& Parameters);

    UFUNCTION(BlueprintImplementableEvent, Category = "Sage Extension Delegator")
    TArray<FString> GetAvailableActions();

    UFUNCTION(BlueprintImplementableEvent, Category = "Sage Extension Delegator")
    TArray<FString> GetAvailableQueries();

    // Native interface methods (can be overridden in C++)
    virtual void ExecuteAction_Native(const FString& ActionName, const FString& Parameters) {}
    virtual FString ExecuteQuery_Native(const FString& QueryName, const FString& Parameters) { return FString(); }
    virtual TArray<FString> GetAvailableActions_Native() { return TArray<FString>(); }
    virtual TArray<FString> GetAvailableQueries_Native() { return TArray<FString>(); }

    // Bridge to original C++ interface
    virtual ISageExtensionDelegator* GetNativeExtensionDelegator() const { return nullptr; }
};

/**
 * Bridge class that implements the UInterface and wraps the original C++ interface
 * This allows existing C++ code to work with the new UInterface system
 */
UCLASS(BlueprintType)
class SAGEUI_API UDruidsChatRequestHandlerBridge : public UObject, public IDruidsChatRequestHandler
{
    GENERATED_BODY()

public:
    UDruidsChatRequestHandlerBridge();

    // Set the native C++ interface to bridge to
    void SetNativeChatRequestHandler(TSharedPtr<IChatRequestHandler> InNativeHandler);

    // IDruidsChatRequestHandler implementation
    virtual void HandleChatRequest_Native(const FString& Message, const FString& Context) override;
    virtual void HandleStreamingResponse_Native(const FString& PartialContent) override;
    virtual void OnRequestCompleted_Native(bool bSuccess, const FString& ErrorMessage) override;
    virtual IChatRequestHandler* GetNativeChatRequestHandler() const override;

protected:
    // Reference to the original C++ interface
    TSharedPtr<IChatRequestHandler> NativeChatRequestHandler;
};

/**
 * Bridge class that implements the UInterface and wraps the original C++ interface
 */
UCLASS(BlueprintType)
class SAGEUI_API UDruidsSageExtensionDelegatorBridge : public UObject, public IDruidsSageExtensionDelegator
{
    GENERATED_BODY()

public:
    UDruidsSageExtensionDelegatorBridge();

    // Set the native C++ interface to bridge to
    void SetNativeExtensionDelegator(TSharedPtr<ISageExtensionDelegator> InNativeDelegator);

    // IDruidsSageExtensionDelegator implementation
    virtual void ExecuteAction_Native(const FString& ActionName, const FString& Parameters) override;
    virtual FString ExecuteQuery_Native(const FString& QueryName, const FString& Parameters) override;
    virtual TArray<FString> GetAvailableActions_Native() override;
    virtual TArray<FString> GetAvailableQueries_Native() override;
    virtual ISageExtensionDelegator* GetNativeExtensionDelegator() const override;

protected:
    // Reference to the original C++ interface
    TSharedPtr<ISageExtensionDelegator> NativeExtensionDelegator;
};

/**
 * Factory class for creating bridge objects
 * Simplifies the creation of UInterface bridges from C++ interfaces
 */
UCLASS(BlueprintType)
class SAGEUI_API UDruidsChatInterfaceBridgeFactory : public UObject
{
    GENERATED_BODY()

public:
    // Create a UInterface bridge from a C++ IChatRequestHandler
    UFUNCTION(BlueprintCallable, Category = "Interface Bridge Factory")
    static TScriptInterface<IDruidsChatRequestHandler> CreateChatRequestHandlerBridge(UObject* Outer, TSharedPtr<IChatRequestHandler> NativeHandler);

    // Create a UInterface bridge from a C++ ISageExtensionDelegator
    UFUNCTION(BlueprintCallable, Category = "Interface Bridge Factory")
    static TScriptInterface<IDruidsSageExtensionDelegator> CreateExtensionDelegatorBridge(UObject* Outer, TSharedPtr<ISageExtensionDelegator> NativeDelegator);

    // Helper to extract native interface from bridge
    UFUNCTION(BlueprintCallable, Category = "Interface Bridge Factory")
    static IChatRequestHandler* GetNativeChatRequestHandler(const TScriptInterface<IDruidsChatRequestHandler>& Bridge);

    UFUNCTION(BlueprintCallable, Category = "Interface Bridge Factory")
    static ISageExtensionDelegator* GetNativeExtensionDelegator(const TScriptInterface<IDruidsSageExtensionDelegator>& Bridge);
};
