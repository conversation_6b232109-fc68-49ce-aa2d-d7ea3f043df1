#pragma once

#include "CoreMinimal.h"
#include "UMG/DruidsChatWidgetBase.h"
#include "UMG/DruidsChatInterfaces.h"
#include "DruidsSageChatTypes.h"
#include "DruidsSageChatView.generated.h"

// Forward declarations
class USc<PERSON>Box;
class UVerticalBox;
class UMultiLineEditableTextBox;
class UButton;
class UTextBlock;
class UUserWidget;
class IChatRequestHandler;
class ISageExtensionDelegatorImplementation;

DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnChatViewMessageSending);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnMessageReceived, const FDruidsSageChatMessage&, Message);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnContextChanged, const FString&, Context, const FString&, DisplayMessage);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnStreamingUpdate, const FString&, PartialContent);

/**
 * UMG version of the chat view widget
 * Main chat interface with message display and input
 */
UCLASS(BlueprintType, Blueprintable)
class SAGEUI_API UDruidsSageChatView : public UDruidsChatWidgetBase
{
    GENERATED_BODY()

public:
    UDruidsSageChatView(const FObjectInitializer& ObjectInitializer);

    // Event delegates
    UPROPERTY(BlueprintAssignable, Category = "Chat Events")
    FOnChatViewMessageSending OnMessageSending;

    UPROPERTY(BlueprintAssignable, Category = "Chat Events")
    FOnMessageReceived OnMessageReceived;

    UPROPERTY(BlueprintAssignable, Category = "Chat Events")
    FOnContextChanged OnContextChanged;

    UPROPERTY(BlueprintAssignable, Category = "Chat Events")
    FOnStreamingUpdate OnStreamingUpdate;

    // Blueprint events for customization
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat View Events")
    void OnMessageReceivedEvent(const FDruidsSageChatMessage& Message);
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat View Events")
    void OnContextChangedEvent(const FString& Context, const FString& DisplayMessage);
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat View Events")
    void OnStreamingUpdateEvent(const FString& PartialContent);

    // Public interface
    UFUNCTION(BlueprintCallable, Category = "Chat View")
    void SendMessage(const FString& MessageText, EDruidsSageChatRole Role = EDruidsSageChatRole::User);
    
    UFUNCTION(BlueprintCallable, Category = "Chat View")
    void ClearChat();
    
    UFUNCTION(BlueprintCallable, Category = "Chat View")
    void SetTabContext(const FString& Context, const FString& DisplayMessage);
    
    UFUNCTION(BlueprintCallable, Category = "Chat View")
    void SetBlueprintContext(const FString& Context, const FString& DisplayMessage);

    UFUNCTION(BlueprintCallable, Category = "Chat View")
    void SetSessionID(const FString& NewSessionID);

    UFUNCTION(BlueprintCallable, Category = "Chat View")
    FString GetSessionID() const { return SessionID; }

    UFUNCTION(BlueprintCallable, Category = "Chat View")
    void SetChatRequestHandler(const TScriptInterface<IDruidsChatRequestHandler>& Handler);

    UFUNCTION(BlueprintCallable, Category = "Chat View")
    void SetExtensionDelegator(const TScriptInterface<IDruidsSageExtensionDelegator>& Delegator);

    UFUNCTION(BlueprintCallable, Category = "Chat View")
    void LoadChatHistory();

    UFUNCTION(BlueprintCallable, Category = "Chat View")
    void SaveChatHistory();

    UFUNCTION(BlueprintCallable, Category = "Chat View")
    int32 GetMessageCount() const { return ChatItems.Num(); }

protected:
    // Widget references (can be bound in Blueprint Designer)
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    UScrollBox* ChatScrollBox;
    
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    UVerticalBox* ChatMessageContainer;
    
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    UMultiLineEditableTextBox* MessageInputBox;
    
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    UButton* SendButton;
    
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    UButton* ClearButton;
    
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    UVerticalBox* ContextChipContainer;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    UTextBlock* ContextLabel;

    // Internal state
    UPROPERTY(BlueprintReadOnly, Category = "Chat View State")
    FString SessionID;
    
    UPROPERTY(BlueprintReadOnly, Category = "Chat View State")
    TArray<UUserWidget*> ChatItems;
    
    UPROPERTY()
    TScriptInterface<IDruidsChatRequestHandler> ChatRequestHandler;

    UPROPERTY()
    TScriptInterface<IDruidsSageExtensionDelegator> ExtensionDelegator;

    // Context information
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    FString CurrentTabContext;
    
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    FString CurrentBlueprintContext;

    UPROPERTY(BlueprintReadOnly, Category = "Context")
    FString CurrentContextDisplay;

    // Message history
    UPROPERTY(BlueprintReadOnly, Category = "Message History")
    TArray<FDruidsSageChatMessage> MessageHistory;

    // Blueprint implementable functions for customization
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat View Customization")
    UUserWidget* CreateChatItem(EDruidsSageChatRole Role, const FDruidsSageChatMessage& Message);
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat View Customization")
    void OnChatItemCreated(UUserWidget* ChatItem);

    UFUNCTION(BlueprintImplementableEvent, Category = "Chat View Customization")
    void OnChatHistoryLoaded();

    UFUNCTION(BlueprintImplementableEvent, Category = "Chat View Customization")
    void OnChatCleared();

    // Native implementations
    virtual void NativeConstruct() override;
    virtual void NativeDestruct() override;
    
    // Button event handlers
    UFUNCTION()
    void OnSendButtonClicked();
    
    UFUNCTION()
    void OnClearButtonClicked();
    
    UFUNCTION()
    void OnMessageInputTextChanged(const FText& Text);

    UFUNCTION()
    void OnMessageInputTextCommitted(const FText& Text, ETextCommit::Type CommitMethod);

    // Message management
    UFUNCTION(BlueprintCallable, Category = "Message Management")
    void AddChatItem(UUserWidget* ChatItem);

    UFUNCTION(BlueprintCallable, Category = "Message Management")
    void AddMessageToHistory(const FDruidsSageChatMessage& Message);

    UFUNCTION(BlueprintCallable, Category = "Message Management")
    UUserWidget* CreateChatItemInternal(EDruidsSageChatRole Role, const FDruidsSageChatMessage& Message);

    // Context management
    UFUNCTION(BlueprintCallable, Category = "Context Management")
    void UpdateContextDisplay();

    UFUNCTION(BlueprintCallable, Category = "Context Management")
    FString GetCurrentContextString() const;

    // UI helpers
    UFUNCTION(BlueprintCallable, Category = "UI Helpers")
    void ScrollToBottom();

    UFUNCTION(BlueprintCallable, Category = "UI Helpers")
    void FocusMessageInput();

    UFUNCTION(BlueprintCallable, Category = "UI Helpers")
    void UpdateSendButtonState();

private:
    // Internal state
    bool bIsInitialized = false;
    bool bIsStreaming = false;
    UUserWidget* CurrentStreamingItem = nullptr;

    // Constants
    static const FString HistoryFileExtension;
    static const int32 MaxHistoryItems;

    // Internal helpers
    void InitializeChatView();
    void CleanupChatView();
    void ProcessMessageSending(const FString& MessageText);
    void HandleStreamingResponse(const FString& PartialContent);
    FString GetHistoryFilePath() const;
    bool IsValidMessage(const FString& MessageText) const;
};
