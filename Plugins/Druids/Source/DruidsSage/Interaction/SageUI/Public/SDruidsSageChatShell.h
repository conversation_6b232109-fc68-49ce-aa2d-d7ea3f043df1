#pragma once

#include <CoreMinimal.h>
#include <Widgets/SCompoundWidget.h>
#include <Widgets/Views/SListView.h>


class IChatRequestHandler;
class ISageExtensionDelegatorImplementation;
class SDruidsSageChatView;

class SAGEUI_API SDruidsSageChatShell final : public SCompoundWidget
{
public:
	// Forward the message sending delegate
	DECLARE_DELEGATE(FOnMessageSending)
	FOnMessageSending OnMessageSending;

	SLATE_BEGIN_ARGS(SDruidsSageChatShell)
		: _ChatRequestHandler()
		, _ExtensionDelegator()
	{}
		SLATE_ARGUMENT(TSharedPtr<IChatRequestHandler>, ChatRequestHandler)
		SLATE_ARGUMENT(TSharedPtr<ISageExtensionDelegatorImplementation>, ExtensionDelegator)
	SLATE_END_ARGS()

	void Construct(const FArguments& InArgs);
	virtual ~SDruidsSageChatShell() override;

	// Expose CurrentView publicly
	TSharedPtr<SDruidsSageChatView> GetCurrentView() const { return CurrentView; }
	
private:
	TSharedRef<SWidget> ConstructContent();

	void InitializeChatSessionOptions();
	void InitializeChatSession(const TSharedPtr<FName>& InItem);

	TSharedPtr<IChatRequestHandler> ChatRequestHandler;
	TSharedPtr<ISageExtensionDelegatorImplementation> ExtensionDelegator;

	TSharedPtr<class SBox> ShellBox;
	TSharedPtr<class SDruidsSageChatView> CurrentView;

	// Add delegate to relay messages from chat view
	void OnChatViewMessageSending() const
	{
		OnMessageSending.ExecuteIfBound();
	}

	TSharedPtr<class SListView<TSharedPtr<FName>>> ChatSessionListView;
	TArray<TSharedPtr<FName>> ChatSessions;

	TSharedRef<ITableRow> OnGenerateChatSessionRow(TSharedPtr<FName> InItem, const TSharedRef<STableViewBase>& OwnerTable);

	void OnChatSessionSelectionChanged(const TSharedPtr<FName> InItem, ESelectInfo::Type SelectInfo);
	void OnChatSessionNameChanged(const TSharedPtr<FName> InItem, const FName& NewName);
	void OnChatSessionDoubleClicked(const TSharedPtr<FName> InItem);
	FReply OnChatSessionKeyDown(const FGeometry& MyGeometry, const FKeyEvent& InKeyEvent);
};
