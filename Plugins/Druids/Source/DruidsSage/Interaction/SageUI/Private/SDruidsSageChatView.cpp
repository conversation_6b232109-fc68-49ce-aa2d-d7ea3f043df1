#include "SDruidsSageChatView.h"

#include <Dom/JsonObject.h>
#include <Serialization/JsonWriter.h>
#include <Serialization/JsonReader.h>
#include <Serialization/JsonSerializer.h>
#include <Misc/FileHelper.h>

#include <Widgets/Layout/SScrollBox.h>
#include <Widgets/Input/SMultiLineEditableTextBox.h>
#include <Framework/Application/SlateApplication.h>
#include <Input/Events.h>
#include <Engine/Engine.h>
#include <Engine/World.h>

#include "LogDruids.h"

#include"DruidsSageHelper.h"

#include "IChatRequestHandler.h"

#include "SDruidsSageSimpleChatItem.h"
#include "SDruidsSageAssistantChatItem.h"
#include "ISageExtensionDelegator.h"

/**
 * Custom multi-line text box that handles Enter key to send messages
 */
class SCustomMultiLineEditableTextBox : public SMultiLineEditableTextBox
{
public:
	SLATE_BEGIN_ARGS(SCustomMultiLineEditableTextBox) {}
		SLATE_EVENT(FSimpleDelegate, OnEnterPressed)
	SLATE_END_ARGS()

	void Construct(const FArguments& InArgs)
	{
		OnEnterPressed = InArgs._OnEnterPressed;

		// Configure the multi-line text box with proper settings
		SMultiLineEditableTextBox::Construct(
			SMultiLineEditableTextBox::FArguments()
			.AllowContextMenu(true)
			.IsReadOnly(false)
			.AutoWrapText(true)
			.WrapTextAt(0.0f) // Auto-wrap based on widget width
			.Margin(FMargin(4.0f))
			.OnKeyDownHandler(this, &SCustomMultiLineEditableTextBox::OnKeyDown)
		);
	}

	virtual FReply OnKeyDown(const FGeometry& MyGeometry, const FKeyEvent& InKeyEvent) override
	{
		// Handle Enter key to send message (without modifiers) - backup method
		if (InKeyEvent.GetKey() == EKeys::Enter && !InKeyEvent.IsShiftDown() && !InKeyEvent.IsControlDown() && !InKeyEvent.IsAltDown())
		{
			if (OnEnterPressed.IsBound())
			{
				OnEnterPressed.Execute();
				return FReply::Handled();
			}
		}

		// Let the base class handle all other keys
		return SMultiLineEditableTextBox::OnKeyDown(MyGeometry, InKeyEvent);
	}

private:
	FSimpleDelegate OnEnterPressed;
};

void SDruidsSageChatView::Construct(const FArguments& InArgs)
{
	CurrentTabContext = TEXT("");
	CurrentTabContextDisplayMessage = TEXT("");
	CurrentBPContext = TEXT("");
	CurrentBPContextDisplayMessage = TEXT("");
	
	SetSessionID(InArgs._SessionID);

	ChildSlot
	[
		ConstructContent()
	];

	LoadChatHistory();

	// Set focus to the input text box after a short delay to ensure window is fully constructed
	if (GWorld)
	{
		GWorld->GetTimerManager().SetTimerForNextTick([this]()
		{
			SetInputFocus();
		});
	}
}

SDruidsSageChatView::~SDruidsSageChatView()
{
	SaveChatHistory();

	ClearChat();
	
	ChatItems.Empty();
	
	if (ChatBox.IsValid())
	{
		ChatBox->ClearChildren();
	}
}

bool SDruidsSageChatView::IsSendMessageEnabled() const
{
	const bool bNoActiveRequest = ChatRequestHandler.IsValid() && ChatRequestHandler.Get()->IsNoActiveRequest();

	return bNoActiveRequest && !InputTextBox->GetText().IsEmpty();
}

bool SDruidsSageChatView::IsClearChatEnabled() const
{
	return !ChatItems.IsEmpty();
}

FString SDruidsSageChatView::GetHistoryPath() const
{
	return FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("DruidsSage"), SessionID.ToString() + TEXT(".json"));
}

void SDruidsSageChatView::SetSessionID(const FName& NewSessionID)
{
	const FName NewValidSessionID = *FPaths::MakeValidFileName(NewSessionID.ToString());
	if (SessionID == NewValidSessionID)
	{
		return;
	}

	if (SessionID.IsNone())
	{
		SessionID = NewValidSessionID;
		return;
	}

	if (const FString OldPath = GetHistoryPath(); FPaths::FileExists(OldPath))
	{
		IFileManager::Get().Delete(*OldPath, true, true, true);
	}

	SessionID = NewValidSessionID;
	SaveChatHistory();
}

FName SDruidsSageChatView::GetSessionID() const
{
	return SessionID;
}

void SDruidsSageChatView::ClearChat()
{
	// First stop any ongoing request
	if (ChatRequestHandler.IsValid())
	{
		ChatRequestHandler.Get()->StopAndCleanupRequest(ChatItems);
	}
	
	ChatItems.Empty();
	if (ChatBox.IsValid())
	{
		ChatBox->ClearChildren();
	}
	
	// Update visibility of both chips
	if (TabContextChipWidget.IsValid())
	{
		TabContextChipWidget->SetVisibility(CurrentTabContextDisplayMessage.IsEmpty() ? EVisibility::Collapsed : EVisibility::Visible);
	}
	if (BPContextChipWidget.IsValid())
	{
		BPContextChipWidget->SetVisibility(CurrentBPContextDisplayMessage.IsEmpty() ? EVisibility::Collapsed : EVisibility::Visible);
	}
}

void SDruidsSageChatView::SetTabContext(const FString& Context, const FString& ContextDisplayMessage)
{
	CurrentTabContext = Context;
	CurrentTabContextDisplayMessage = ContextDisplayMessage;

	FString TruncatedMessage = TruncateString(ContextDisplayMessage);

	if (TabContextChipWidget.IsValid())
	{
		TabContextChipWidget->SetText(TruncatedMessage);
		TabContextChipWidget->SetVisibility(TruncatedMessage.IsEmpty() ? EVisibility::Collapsed : EVisibility::Visible);
	}
}

void SDruidsSageChatView::SetBPContext(const FString& BPContext, const FString& BPContextDisplayMessage)
{
	CurrentBPContext = BPContext;
	CurrentBPContextDisplayMessage = BPContextDisplayMessage;

	FString TruncatedMessage = TruncateString(BPContextDisplayMessage);

	if (BPContextChipWidget.IsValid())
	{
		BPContextChipWidget->SetText(TruncatedMessage);
		BPContextChipWidget->SetVisibility(TruncatedMessage.IsEmpty() ? EVisibility::Collapsed : EVisibility::Visible);
	}
}

void SDruidsSageChatView::SetActiveObject(const TWeakObjectPtr<>& NewActiveObject)
{
	ActiveObject = NewActiveObject;
	UE_LOG(LogDruidsSage_Internal, Display, TEXT("Active Object Set: %s"), *GetNameSafe(NewActiveObject.Get()));
}

void SDruidsSageChatView::SetActiveExtensionDefinitions(
	const TArray<TSharedPtr<FDruidsSageExtensionDefinition>>& NewActiveExtensions)
{
	ActiveExtensionDefinitions = NewActiveExtensions;
}

TSharedRef<SWidget> SDruidsSageChatView::ConstructContent()
{
	constexpr float SlotPadding = 4.0f;
	constexpr float ChipPadding = 2.0f; // Padding between chips

	return SNew(SVerticalBox)
		+ SVerticalBox::Slot().Padding(SlotPadding).FillHeight(1.f)
		[
			SAssignNew(ChatScrollBox, SScrollBox)
			+ SScrollBox::Slot()
			[
				SAssignNew(ChatBox, SVerticalBox)
			]
		]
		+ SVerticalBox::Slot().Padding(SlotPadding).AutoHeight()
		[
			SAssignNew(ContextChipStack, SVerticalBox)
			// BP Context Chip Slot
			+ SVerticalBox::Slot()
			.AutoHeight()
			.Padding(0, 0, 0, ChipPadding) // Add padding below this chip
			[
				SAssignNew(BPContextChipWidget, SContextChipWidget)
				.Text(CurrentBPContextDisplayMessage) // Initial text
				.IsRemovable(false)
				.ChipColor(FLinearColor(0.1f, 0.1f, 0.4f)) // Different color for BP
				.TextColor(FLinearColor::White)
				.Visibility(EVisibility::Collapsed) // Initially hidden
			]
			// Tab Context Chip Slot
			+ SVerticalBox::Slot()
			.AutoHeight()
			[
				SAssignNew(TabContextChipWidget, SContextChipWidget)
				.Text(CurrentTabContextDisplayMessage) // Initial text
				.IsRemovable(false)
				.ChipColor(FLinearColor(0.2f, 0.4f, 0.8f)) // Original color
				.TextColor(FLinearColor::White)
				.Visibility(EVisibility::Collapsed) // Initially hidden
			]
		]
		// Text input area - can expand vertically
		+ SVerticalBox::Slot().Padding(SlotPadding).AutoHeight()
		[
			SAssignNew(InputTextBox, SCustomMultiLineEditableTextBox)
			.OnEnterPressed_Lambda([this]()
			{
				if (IsSendMessageEnabled())
				{
					HandleSendMessageButton(EDruidsSageChatRole::User);
				}
			})
		]
		// Button area - fixed height
		+ SVerticalBox::Slot().Padding(SlotPadding).AutoHeight()
		[
			SNew(SHorizontalBox)
			+ SHorizontalBox::Slot().Padding(SlotPadding).AutoWidth()
			[
				SNew(SButton)
				.Text(FText::FromString(TEXT("Send")))
				.ToolTipText(FText::FromString(TEXT("Send Message")))
				.OnClicked(this, &SDruidsSageChatView::HandleSendMessageButton, EDruidsSageChatRole::User)
				.IsEnabled(this, &SDruidsSageChatView::IsSendMessageEnabled)
			]
			+ SHorizontalBox::Slot().Padding(SlotPadding).AutoWidth()
			[
				SNew(SButton)
				.Text(FText::FromString(TEXT("Clear")))
				.ToolTipText(FText::FromString(TEXT("Clear Chat History")))
				.OnClicked(this, &SDruidsSageChatView::HandleClearChatButton)
				.IsEnabled(this, &SDruidsSageChatView::IsClearChatEnabled)
			]
		];
}

TSharedPtr<IDruidsSageChatItem> SDruidsSageChatView::CreateChatItem(
	EDruidsSageChatRole Role,
	const FString& ChatText) const
{
	if (Role == EDruidsSageChatRole::Assistant)
	{
		TSharedPtr<SDruidsSageAssistantChatItem> AssistantChatItem = SNew(SDruidsSageAssistantChatItem)
			.OnActionApplied(SDruidsSageAssistantChatItem::FOnActionApplied::CreateSP(
				this, &SDruidsSageChatView::OnActionRequestApplied));

		AssistantChatItem.Get()->SetRawText(ChatText);
		
		return AssistantChatItem;
	}
	
	return SNew(SDruidsSageSimpleChatItem)
		.MessageRole(Role)
		.ChatText(ChatText)
		.ScrollBox(ChatScrollBox)
		.ActiveExtensionDefinitions(ActiveExtensionDefinitions);
}

void SDruidsSageChatView::OnActionRequestApplied(const TSharedPtr<FJsonValue>& ActionDetails) const
{
	if (ExtensionDelegator.IsValid())
	{
		ExtensionDelegator.Get()->OnActionApplied(ActionDetails);
	}
}

FReply SDruidsSageChatView::HandleSendMessageButton(const EDruidsSageChatRole Role)
{
	// Broadcast that we're about to send a message
	OnMessageSending.ExecuteIfBound();

	FString ChatText(InputTextBox->GetText().ToString());
	const TSharedPtr<IDruidsSageChatItem> NewUserChatItem = CreateChatItem(
		Role,
		ChatText
	);

	ChatBox->AddSlot().AutoHeight()[NewUserChatItem.ToSharedRef()];
	ChatItems.Add(NewUserChatItem);

	if (Role == EDruidsSageChatRole::System)
	{
		ChatScrollBox->ScrollToEnd();
		InputTextBox->SetText(FText::GetEmpty());
		return FReply::Handled();
	}

	const TSharedPtr<IDruidsSageChatItem> AssistantMessage = CreateChatItem(
		EDruidsSageChatRole::Assistant,
		FString()
	);

	// *** Combine Contexts for the AI Request ***
	// Simple concatenation for now. Might need more sophisticated formatting later.
	FString CombinedContext = "";
	if (!CurrentTabContext.IsEmpty())
	{
		CombinedContext += CurrentTabContext;
	}
	if (!CurrentBPContext.IsEmpty())
	{
		if (!CombinedContext.IsEmpty())
		{
			CombinedContext += "\n\n"; // Separator
		}
		CombinedContext += "Blueprint Context (current BP nodes selected):\n" + CurrentBPContext;
	}
	// ********************************************

	if (ChatRequestHandler.IsValid())
	{
		ChatRequestHandler.Get()->SetupAndSendRequest(ChatItems, AssistantMessage, CombinedContext);
	}
	
	ChatBox->AddSlot().AutoHeight()[AssistantMessage.ToSharedRef()];
	ChatItems.Add(AssistantMessage);

	ChatScrollBox->ScrollToEnd();
	InputTextBox->SetText(FText::GetEmpty());

	return FReply::Handled();
}

FReply SDruidsSageChatView::HandleClearChatButton()
{
	ClearChat();
	return FReply::Handled();
}

TArray<FDruidsSageChatMessage> SDruidsSageChatView::GetChatHistory() const
{
	TArray<FDruidsSageChatMessage> Output;
	
	for (const TSharedPtr<IDruidsSageChatItem>& Item : ChatItems)
	{
		FDruidsSageChatMessage DruidsMessage;
		Item->FillInDruidsMessage(DruidsMessage);
		Output.Add(DruidsMessage);
	}

	return Output;
}

void SDruidsSageChatView::LoadChatHistory()
{
	ChatItems.Empty();
	if (ChatBox.IsValid())
	{
		ChatBox->ClearChildren();
	}

	SetTabContext(TEXT(""), TEXT(""));
	SetBPContext(TEXT(""), TEXT(""));
	
	if (SessionID.IsNone())
	{
		return;
	}

	if (const FString LoadPath = GetHistoryPath(); FPaths::FileExists(LoadPath))
	{
		FString FileContent;
		if (!FFileHelper::LoadFileToString(FileContent, *LoadPath))
		{
			return;
		}

		TSharedPtr<FJsonObject> JsonParsed;
		const TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(FileContent);
		if (FJsonSerializer::Deserialize(Reader, JsonParsed))
		{
			const TArray<TSharedPtr<FJsonValue>> SessionData = JsonParsed->GetArrayField(TEXT("Data"));
			for (const TSharedPtr<FJsonValue>& Item : SessionData)
			{
				if (const TSharedPtr<FJsonObject> MessageItObj = Item->AsObject())
				{
					if (FString RoleString; MessageItObj->TryGetStringField(TEXT("role"), RoleString))
					{
						const EDruidsSageChatRole Role = UDruidsSageHelper::NameToRole(*RoleString);

						if (FString ChatMessage;
							MessageItObj->TryGetStringField(TEXT("content"), ChatMessage))
						{
							if (Role == EDruidsSageChatRole::System)
							{
								continue;
							}

							ChatItems.Emplace(CreateChatItem(
								Role,
								ChatMessage
							));
						}
						else if (const TArray<TSharedPtr<FJsonValue>>* ContentArray; MessageItObj->TryGetArrayField(TEXT("content"), ContentArray))
						{
							for (TSharedPtr ContentValue : *ContentArray)
							{
								if (TSharedPtr<FJsonObject> ContentObj = ContentValue->AsObject())
								{
									if (FString ContentType;
										ContentObj->TryGetStringField(TEXT("type"), ContentType) &&
										ContentType == TEXT("text"))
									{
										if (FString TextContent;
											ContentObj->TryGetStringField(TEXT("text"), TextContent))
										{
											ChatItems.Emplace(CreateChatItem(Role, TextContent));
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}

	for (const TSharedPtr<IDruidsSageChatItem>& Item : ChatItems)
	{
		ChatBox->AddSlot().AutoHeight()[Item.ToSharedRef()];
	}
}

void SDruidsSageChatView::SaveChatHistory() const
{
	if (SessionID.IsNone())
	{
		return;
	}

	const TSharedPtr<FJsonObject> JsonRequest = MakeShared<FJsonObject>();

	TArray<TSharedPtr<FJsonValue>> Data;
	for (const FDruidsSageChatMessage& Item : GetChatHistory())
	{
		Data.Add(Item.GetMessageJson());
	}

	JsonRequest->SetArrayField("Data", Data);

	FString RequestContentString;
	const TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&RequestContentString);

	if (FJsonSerializer::Serialize(JsonRequest.ToSharedRef(), Writer))
	{
		FFileHelper::SaveStringToFile(RequestContentString, *GetHistoryPath());
	}
}

void SDruidsSageChatView::SetChatRequestHandler(const TWeakPtr<IChatRequestHandler>& NewChatRequestHandler)
{
	ChatRequestHandler = NewChatRequestHandler.Pin().ToSharedRef();
}

void SDruidsSageChatView::SetExtensionsDelegator(const TWeakPtr<ISageExtensionDelegator>& NewExtensionDelegator)
{
	ExtensionDelegator = NewExtensionDelegator.Pin();
}

FString SDruidsSageChatView::TruncateString(const FString& Input, int32 MaxLength /*= 100*/) const
{
	if (Input.Len() > MaxLength)
	{
		return Input.Left(MaxLength - 3) + TEXT("...");
	}
	return Input;
}

void SDruidsSageChatView::SetInputFocus()
{
	if (InputTextBox.IsValid())
	{
		FSlateApplication::Get().SetKeyboardFocus(InputTextBox);
	}
}
