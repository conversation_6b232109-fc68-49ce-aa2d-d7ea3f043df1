#include "UMG/DruidsChatInterfaces.h"
#include "IChatRequestHandler.h"
#include "ISageExtensionDelegator.h"
#include "LogDruids.h"

// UDruidsChatRequestHandlerBridge Implementation

UDruidsChatRequestHandlerBridge::UDruidsChatRequestHandlerBridge()
{
    NativeChatRequestHandler = nullptr;
}

void UDruidsChatRequestHandlerBridge::SetNativeChatRequestHandler(TSharedPtr<IChatRequestHandler> InNativeHandler)
{
    NativeChatRequestHandler = InNativeHandler;
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatRequestHandlerBridge::SetNativeChatRequestHandler - Native handler set"));
}

void UDruidsChatRequestHandlerBridge::HandleChatRequest_Native(const FString& Message, const FString& Context)
{
    // Call Blueprint implementation first
    HandleChatRequest(Message, Context);
    
    // Then call native implementation if available
    if (NativeChatRequestHandler.IsValid())
    {
        // Note: This would need to be adapted based on the actual IChatRequestHandler interface
        // For now, we'll log that we would call the native handler
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatRequestHandlerBridge::HandleChatRequest_Native - Would call native handler with message: %s"), *Message);
    }
}

void UDruidsChatRequestHandlerBridge::HandleStreamingResponse_Native(const FString& PartialContent)
{
    // Call Blueprint implementation first
    HandleStreamingResponse(PartialContent);
    
    // Then call native implementation if available
    if (NativeChatRequestHandler.IsValid())
    {
        UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsChatRequestHandlerBridge::HandleStreamingResponse_Native - Would call native handler with content length: %d"), PartialContent.Len());
    }
}

void UDruidsChatRequestHandlerBridge::OnRequestCompleted_Native(bool bSuccess, const FString& ErrorMessage)
{
    // Call Blueprint implementation first
    OnRequestCompleted(bSuccess, ErrorMessage);
    
    // Then call native implementation if available
    if (NativeChatRequestHandler.IsValid())
    {
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatRequestHandlerBridge::OnRequestCompleted_Native - Request completed: %s"), bSuccess ? TEXT("Success") : TEXT("Failed"));
    }
}

IChatRequestHandler* UDruidsChatRequestHandlerBridge::GetNativeChatRequestHandler() const
{
    return NativeChatRequestHandler.Get();
}

// UDruidsSageExtensionDelegatorBridge Implementation

UDruidsSageExtensionDelegatorBridge::UDruidsSageExtensionDelegatorBridge()
{
    NativeExtensionDelegator = nullptr;
}

void UDruidsSageExtensionDelegatorBridge::SetNativeExtensionDelegator(TSharedPtr<ISageExtensionDelegator> InNativeDelegator)
{
    NativeExtensionDelegator = InNativeDelegator;
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageExtensionDelegatorBridge::SetNativeExtensionDelegator - Native delegator set"));
}

void UDruidsSageExtensionDelegatorBridge::ExecuteAction_Native(const FString& ActionName, const FString& Parameters)
{
    // Call Blueprint implementation first
    ExecuteAction(ActionName, Parameters);
    
    // Then call native implementation if available
    if (NativeExtensionDelegator.IsValid())
    {
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageExtensionDelegatorBridge::ExecuteAction_Native - Would execute action: %s"), *ActionName);
    }
}

FString UDruidsSageExtensionDelegatorBridge::ExecuteQuery_Native(const FString& QueryName, const FString& Parameters)
{
    // Call Blueprint implementation first
    FString BlueprintResult = ExecuteQuery(QueryName, Parameters);
    
    // Then call native implementation if available
    if (NativeExtensionDelegator.IsValid())
    {
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageExtensionDelegatorBridge::ExecuteQuery_Native - Would execute query: %s"), *QueryName);
        // Return Blueprint result for now, could combine with native result
        return BlueprintResult;
    }
    
    return BlueprintResult;
}

TArray<FString> UDruidsSageExtensionDelegatorBridge::GetAvailableActions_Native()
{
    // Call Blueprint implementation first
    TArray<FString> BlueprintActions = GetAvailableActions();
    
    // Then call native implementation if available
    if (NativeExtensionDelegator.IsValid())
    {
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageExtensionDelegatorBridge::GetAvailableActions_Native - Would get native actions"));
        // Return Blueprint actions for now, could combine with native actions
        return BlueprintActions;
    }
    
    return BlueprintActions;
}

TArray<FString> UDruidsSageExtensionDelegatorBridge::GetAvailableQueries_Native()
{
    // Call Blueprint implementation first
    TArray<FString> BlueprintQueries = GetAvailableQueries();
    
    // Then call native implementation if available
    if (NativeExtensionDelegator.IsValid())
    {
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsSageExtensionDelegatorBridge::GetAvailableQueries_Native - Would get native queries"));
        // Return Blueprint queries for now, could combine with native queries
        return BlueprintQueries;
    }
    
    return BlueprintQueries;
}

ISageExtensionDelegator* UDruidsSageExtensionDelegatorBridge::GetNativeExtensionDelegator() const
{
    return NativeExtensionDelegator.Get();
}

// UDruidsChatInterfaceBridgeFactory Implementation

TScriptInterface<IDruidsChatRequestHandler> UDruidsChatInterfaceBridgeFactory::CreateChatRequestHandlerBridge(UObject* Outer, TSharedPtr<IChatRequestHandler> NativeHandler)
{
    if (!Outer)
    {
        UE_LOG(LogDruidsSage, Error, TEXT("UDruidsChatInterfaceBridgeFactory::CreateChatRequestHandlerBridge - Outer is null"));
        return TScriptInterface<IDruidsChatRequestHandler>();
    }
    
    UDruidsChatRequestHandlerBridge* Bridge = NewObject<UDruidsChatRequestHandlerBridge>(Outer);
    if (Bridge && NativeHandler.IsValid())
    {
        Bridge->SetNativeChatRequestHandler(NativeHandler);
    }
    
    TScriptInterface<IDruidsChatRequestHandler> ScriptInterface;
    ScriptInterface.SetObject(Bridge);
    ScriptInterface.SetInterface(Bridge);
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatInterfaceBridgeFactory::CreateChatRequestHandlerBridge - Bridge created"));
    return ScriptInterface;
}

TScriptInterface<IDruidsSageExtensionDelegator> UDruidsChatInterfaceBridgeFactory::CreateExtensionDelegatorBridge(UObject* Outer, TSharedPtr<ISageExtensionDelegator> NativeDelegator)
{
    if (!Outer)
    {
        UE_LOG(LogDruidsSage, Error, TEXT("UDruidsChatInterfaceBridgeFactory::CreateExtensionDelegatorBridge - Outer is null"));
        return TScriptInterface<IDruidsSageExtensionDelegator>();
    }
    
    UDruidsSageExtensionDelegatorBridge* Bridge = NewObject<UDruidsSageExtensionDelegatorBridge>(Outer);
    if (Bridge && NativeDelegator.IsValid())
    {
        Bridge->SetNativeExtensionDelegator(NativeDelegator);
    }
    
    TScriptInterface<IDruidsSageExtensionDelegator> ScriptInterface;
    ScriptInterface.SetObject(Bridge);
    ScriptInterface.SetInterface(Bridge);
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatInterfaceBridgeFactory::CreateExtensionDelegatorBridge - Bridge created"));
    return ScriptInterface;
}

IChatRequestHandler* UDruidsChatInterfaceBridgeFactory::GetNativeChatRequestHandler(const TScriptInterface<IDruidsChatRequestHandler>& Bridge)
{
    if (Bridge.GetInterface())
    {
        return Bridge.GetInterface()->GetNativeChatRequestHandler();
    }
    
    return nullptr;
}

ISageExtensionDelegator* UDruidsChatInterfaceBridgeFactory::GetNativeExtensionDelegator(const TScriptInterface<IDruidsSageExtensionDelegator>& Bridge)
{
    if (Bridge.GetInterface())
    {
        return Bridge.GetInterface()->GetNativeExtensionDelegator();
    }
    
    return nullptr;
}
