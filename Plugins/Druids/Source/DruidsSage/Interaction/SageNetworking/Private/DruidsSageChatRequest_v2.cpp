#include "DruidsSageChatRequest_v2.h"

#include <Interfaces/IHttpRequest.h>
#include <Dom/JsonObject.h>
#include <Serialization/JsonWriter.h>
#include <Serialization/JsonReader.h>
#include <Serialization/JsonSerializer.h>
#include <Misc/ScopeTryLock.h>
#include <Async/Async.h>
#include <HttpModule.h>

#include "LogDruids.h"

#include "DruidsSageHelper.h"
#include "DruidsSageSettings.h"

#include "ISageExtensionDelegator.h"

#if WITH_EDITOR
#include <Editor.h>
#endif

#ifdef UE_INLINE_GENERATED_CPP_BY_NAME
#include UE_INLINE_GENERATED_CPP_BY_NAME(DruidsSageChatRequest_v2)
#endif

#if WITH_EDITOR
UDruidsSageChatRequest_v2* UDruidsSageChatRequest_v2::EditorTask(const TArray<FDruidsSageChatMessage>& Messages,
                                                                 const TSharedPtr<ISageExtensionDelegatorImplementation> ExtensionDelegator,
                                                                 const FString& UserFocusContext)
{
	UDruidsSageChatRequest_v2* const NewAsyncTask = SendMessages(
		GEditor->GetEditorWorldContext().World(), Messages, ExtensionDelegator, UserFocusContext);
	
	NewAsyncTask->bIsEditorTask = true;

	return NewAsyncTask;
}
#endif

UDruidsSageChatRequest_v2* UDruidsSageChatRequest_v2::SendMessages(
	UObject* const WorldContextObject,
	const TArray<FDruidsSageChatMessage>& Messages,
	const TSharedPtr<ISageExtensionDelegatorImplementation> ExtensionDelegator,
	const FString& UserFocusContext)
{
	UDruidsSageChatRequest_v2* const NewAsyncTask = NewObject<UDruidsSageChatRequest_v2>();
	NewAsyncTask->Messages = Messages;
	NewAsyncTask->ExtensionDelegator = ExtensionDelegator;
	NewAsyncTask->UserFocusContext = UserFocusContext;

	NewAsyncTask->RegisterWithGameInstance(WorldContextObject);

	return NewAsyncTask;
}

bool UDruidsSageChatRequest_v2::CanActivateTask()
{
	if (!Super::CanActivateTask())
	{
		return false;
	}

	if (Messages.IsEmpty())
	{
		UE_LOG(LogDruidsSage, Error, TEXT("%s (%d): Can't activate task: Invalid Messages."), *FString(__FUNCTION__), GetUniqueID());
		return false;
	}

	return true;
}

bool UDruidsSageChatRequest_v2::CanBindProgress() const
{
	//Always streaming
	return true;
}

FString UDruidsSageChatRequest_v2::GetEndpointURL() const
{
	return TEXT("https://groveserver.replit.app/sprout/sage/v7a");
}

FString UDruidsSageChatRequest_v2::SetRequestContent()
{
	FScopeLock Lock(&Mutex);

	if (!HttpRequest.IsValid())
	{
		return FString();
	}

	UE_LOG(LogDruidsSage_Internal, Display, TEXT("%s (%d): Mounting content"), *FString(__FUNCTION__), GetUniqueID());

	const TSharedPtr<FJsonObject> JsonRequest = MakeShared<FJsonObject>();

	if (UserFocusContext.Len() > 0)
	{
		JsonRequest->SetStringField("user_focus_context", UserFocusContext);
	}

	TArray<TSharedPtr<FJsonValue>> MessagesJson;
	for (const FDruidsSageChatMessage& Iterator : Messages)
	{
		MessagesJson.Add(Iterator.GetMessageJson());
	}

	JsonRequest->SetArrayField("messages", MessagesJson);

	FString RequestContentString;
	const TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&RequestContentString);
	FJsonSerializer::Serialize(JsonRequest.ToSharedRef(), Writer);

	HttpRequest->SetContentAsString(RequestContentString);

	return RequestContentString;
}

void UDruidsSageChatRequest_v2::OnProgressUpdated(const FString& Content, int32 BytesSent, int32 BytesReceived)
{
	FScopeLock Lock(&Mutex);

	if (Content.IsEmpty())
	{
		return;
	}

	TArray<FString> StreamedResponses = GetStreamedResponsesFromContent(Content);

	UE_LOG(LogDruidsSage_Internal, Display, TEXT("%s (%d): Progress Updated"), *FString(__FUNCTION__), GetUniqueID());
	UE_LOG(LogDruidsSage_Internal, Display, TEXT("%s (%d): Content: %s; Bytes Sent: %d; Bytes Received: %d"), *FString(__FUNCTION__), GetUniqueID(),
	       StreamedResponses.IsEmpty() ? TEXT("<none>") : *StreamedResponses.Top(), BytesSent, BytesReceived);

	ProcessStreamedResponses(StreamedResponses);

	if (!Response.bSuccess)
	{
		return;
	}

	if (!bInitialized)
	{
		bInitialized = true;

		AsyncTask(ENamedThreads::GameThread, [this]
		{
			FScopeLock Lock(&Mutex);
			ProgressStarted.Broadcast(Response);
		});
	}

	AsyncTask(ENamedThreads::GameThread, [this]
	{
		const FScopeTryLock Lock(&Mutex);

		if (Lock.IsLocked())
		{
			ProgressUpdated.Broadcast(Response);
		}
	});
}

void UDruidsSageChatRequest_v2::OnProgressCompleted(const FString& Content, const bool bWasSuccessful)
{
	FScopeLock Lock(&Mutex);

	if (!bWasSuccessful || Content.IsEmpty())
	{
		UE_LOG(LogDruidsSage, Error, TEXT("%s (%d): Request failed"), *FString(__FUNCTION__), GetUniqueID());
		AsyncTask(ENamedThreads::GameThread, [this]
		{
			RequestFailed.Broadcast();
		});

		return;
	}

	UE_LOG(LogDruidsSage_Internal, Display, TEXT("%s (%d): Process Completed"), *FString(__FUNCTION__), GetUniqueID());
	UE_LOG(LogDruidsSage_Internal, Display, TEXT("%s (%d): Content: %s"), *FString(__FUNCTION__), GetUniqueID(), *Content);

	const TArray<FString> StreamedResponses = GetStreamedResponsesFromContent(Content);
	ProcessStreamedResponses(StreamedResponses);

	if (Response.bSuccess)
	{
		AsyncTask(ENamedThreads::GameThread, [this]
		{
			FScopeLock Lock(&Mutex);

			ProcessCompleted.Broadcast(Response);
		});
	}
	else
	{
		UE_LOG(LogDruidsSage, Error, TEXT("%s (%d): Request failed"), *FString(__FUNCTION__), GetUniqueID());
		AsyncTask(ENamedThreads::GameThread, [this]
		{
			FScopeLock Lock(&Mutex);
			ErrorReceived.Broadcast(Response);
		});
	}
}

TArray<FString> UDruidsSageChatRequest_v2::GetStreamedResponsesFromContent(const FString& Content)
{
	TArray<FString> Deltas_In, Deltas_Out;
	Content.ParseIntoArray(Deltas_In, TEXT("\n\n"));
	for (FString Delta_In : Deltas_In)
	{
		if (Delta_In.StartsWith("data:"))
		{
			if (!Delta_In.StartsWith("data: [done]"))
			{
				Deltas_Out.Add(Delta_In.Replace(TEXT("data: "), TEXT("")));
			}
		}
	}
	
	return Deltas_Out;
}

void UDruidsSageChatRequest_v2::ProcessStreamedResponses(const TArray<FString>& StreamedResponses)
{
	FScopeLock Lock(&Mutex);

	Response.bSuccess = true;
	
	Response.Choices.Empty(StreamedResponses.Num());
	for (const FString& StreamedResponse : StreamedResponses)
	{
		ProcessStreamedResponse(StreamedResponse);
	}
}

void UDruidsSageChatRequest_v2::ProcessStreamedResponse(const FString& StreamedResponse)
{
	FScopeLock Lock(&Mutex);

	if (StreamedResponse.IsEmpty())
	{
		return;
	}

	const TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(StreamedResponse);
	TSharedPtr<FJsonObject> JsonResponse = MakeShared<FJsonObject>();
	FJsonSerializer::Deserialize(Reader, JsonResponse);

	if (CheckError(JsonResponse, Response.Error))
	{
		Response.bSuccess = false;
		return;
	}

	const TArray<TSharedPtr<FJsonValue>> *ChoicesArr;
	if (!JsonResponse->TryGetArrayField(TEXT("choices"), ChoicesArr))
	{
		const TArray<TSharedPtr<FJsonValue>> *QueryRequestsArr;
		if (JsonResponse->TryGetArrayField(TEXT("query_requests"), QueryRequestsArr))
		{
			ProcessQueryRequests(QueryRequestsArr);
			return;
		}

		Response.bSuccess = false;
		return;
	}

	for (auto Iterator = ChoicesArr->CreateConstIterator(); Iterator; ++Iterator)
	{
		const TSharedPtr<FJsonObject> ChoiceObj = (*Iterator)->AsObject();
		int32 ChoiceIndex = 0;
		if (ChoiceObj.IsValid() && ChoiceObj->HasField(TEXT("index")))
		{
			ChoiceIndex = ChoiceObj->GetIntegerField(TEXT("index"));
		}

		FDruidsSageChatChoice* Choice = Response.Choices.FindByPredicate([this, ChoiceIndex](const FDruidsSageChatChoice& Element)
		{
			return Element.Index == ChoiceIndex;
		});

		if (!Choice)
		{
			FDruidsSageChatChoice NewChoice;
			NewChoice.Index = ChoiceIndex;
			Choice = &Response.Choices.Add_GetRef(NewChoice);
		}

		if (const TSharedPtr<FJsonObject>* MessageObj; ChoiceObj->TryGetObjectField(TEXT("message"), MessageObj))
		{
			if (FString RoleStr; (*MessageObj)->TryGetStringField(TEXT("role"), RoleStr))
			{
				Choice->Message.SetRole(RoleStr == "user" ? EDruidsSageChatRole::User : EDruidsSageChatRole::Assistant);
			}

			if (FString ContentStr; (*MessageObj)->TryGetStringField(TEXT("content"), ContentStr))
			{
				Choice->Message.SetChatContent(ContentStr);
			}
			else if (const TArray<TSharedPtr<FJsonValue>> *ContentArr;(*MessageObj)->TryGetArrayField(TEXT("content"), ContentArr))
			{
				Choice->Message.SetContentArray(*ContentArr);
			}
		}
		else if (const TSharedPtr<FJsonObject>* DeltaObj; ChoiceObj->TryGetObjectField(TEXT("delta"), DeltaObj))
		{
			if (FString RoleStr; (*DeltaObj)->TryGetStringField(TEXT("role"), RoleStr))
			{
				Choice->Message.SetRole(UDruidsSageHelper::NameToRole(*RoleStr));
			}
			else if (FString ContentStr; (*DeltaObj)->TryGetStringField(TEXT("content"), ContentStr))
			{
				Choice->Message.SetChatContent(Choice->Message.GetChatContent() + ContentStr);
			}
		}
		else if (FString MessageText; ChoiceObj->TryGetStringField(TEXT("text"), MessageText))
		{
			Choice->Message.SetRole(EDruidsSageChatRole::Assistant);
			Choice->Message.SetChatContent(Choice->Message.GetChatContent() + MessageText);
		}

		while (Choice->Message.GetChatContent().StartsWith("\n"))
		{
			Choice->Message.SetChatContent(Choice->Message.GetChatContent().RightChop(1));
		}

		if (FString FinishReasonStr; ChoiceObj->TryGetStringField(TEXT("finish_reason"), FinishReasonStr))
		{
			Choice->FinishReason = *FinishReasonStr;
		}
	}
}

void UDruidsSageChatRequest_v2::ProcessQueryRequests(const TArray<TSharedPtr<FJsonValue>>* QueryRequestsArray)
{
	FString QueryResponseUrl;
	TArray<TSharedPtr<FJsonObject>> QueryResponses;

	/**
	 * Call the pending query requests
	 */
	for (auto Iterator = QueryRequestsArray->CreateConstIterator(); Iterator; ++Iterator)
	{
		if (const TSharedPtr<FJsonObject> QueryRequestObj = (*Iterator)->AsObject(); QueryRequestObj.IsValid())
		{
			if (FString QueryRequestId; QueryRequestObj->TryGetStringField(TEXT("query_request_id"), QueryRequestId))
			{
				if (!QueryResponsesDone.Contains(QueryRequestId))
				{
					TSharedPtr<FJsonObject> ResultsObject = ExtensionDelegator->OnQueryRequested(QueryRequestObj);

					QueryResponsesDone.Add(QueryRequestId);
					QueryResponses.Add(QueryRequestObj);
					
					if (FString ResponseUrlToUse; QueryResponseUrl.IsEmpty() && QueryRequestObj->TryGetStringField(TEXT("response_url"), ResponseUrlToUse))
					{
						QueryResponseUrl = ResponseUrlToUse;
					}
				}
			}
		}
	}

	/*
	 * Package up query responses and send back
	 */
	if (!QueryResponses.IsEmpty())
	{
		//
		// Set up the request
		//
		HttpRequest = FHttpModule::Get().CreateRequest();
		{
			HttpRequest->SetURL(QueryResponseUrl);
			HttpRequest->SetVerb("POST");
			HttpRequest->SetHeader("Content-Type", "application/json");
	
			// Use the DruidsUserToken as the value for the Authorization header
			FString AuthHeader = FString::Printf(TEXT("Bearer %s"), *CommonOptions.APIKey.ToString());
			HttpRequest->SetHeader("Authorization", AuthHeader);
		}

		//
		// Store the request while it is in flight
		//
		FCriticalSection InFlightQueryMutex; TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> StoredRequest;
		{
			const FWindowsCriticalSection* Lock(&InFlightQueryMutex);

			// Store the request to keep it alive until completion
			StoredRequest = HttpRequest;
			InFlightQueryResponses.Add(StoredRequest);
		}

		//
		// Add the query responses
		//
		{
			// Create a JSON object to hold the query responses
			TSharedPtr<FJsonObject> RequestBodyObj = MakeShared<FJsonObject>();
			
			// Create an array of query response objects
			TArray<TSharedPtr<FJsonValue>> QueryResponsesArray;
			for (const TSharedPtr<FJsonObject>& QueryResponse : QueryResponses)
			{
				QueryResponsesArray.Add(MakeShared<FJsonValueObject>(QueryResponse));
			}
			
			// Add the array to the JSON object
			RequestBodyObj->SetArrayField(TEXT("query_responses"), QueryResponsesArray);
			
			// Serialize the JSON object to a string
			FString RequestBodyString;
			TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&RequestBodyString);
			FJsonSerializer::Serialize(RequestBodyObj.ToSharedRef(), Writer);
			
			// Set the request content
			HttpRequest->SetContentAsString(RequestBodyString);
		}

		//
		// Fire the request off
		//
		{
			// Add completion handler to clean up the reference when done
			HttpRequest->OnProcessRequestComplete().BindLambda(
				[this, StoredRequest, &InFlightQueryMutex]
				(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bSuccess)
			{
				// Remove when complete
				const FWindowsCriticalSection* Lock(&InFlightQueryMutex);
				InFlightQueryResponses.Remove(StoredRequest);
			});

			AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [this]
			{
				HttpRequest->ProcessRequest();
			});
		}
	}
}
