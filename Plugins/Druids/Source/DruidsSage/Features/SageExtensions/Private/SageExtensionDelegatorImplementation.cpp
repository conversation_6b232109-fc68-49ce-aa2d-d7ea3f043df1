#include "SageExtensionDelegatorImplementation.h"

#include "SageExtension.h"
#include "ActiveSageExtensions.h"

void FSageExtensionDelegatorImplementation::OnActionApplied(const TSharedPtr<FJsonValue>& ActionDetails)
{
	if (const TSharedPtr<FJsonObject> *ContentObject;
		ActionDetails->TryGetObject(ContentObject) && ContentObject->IsValid())
	{
		if (FString ContentType; ContentObject->Get()->TryGetStringField(TEXT("type"), ContentType))
		{
			if (const TSharedPtr<FJsonObject> *ActionRequestDetails;
				ContentObject->Get()->TryGetObjectField(TEXT("action_request"), ActionRequestDetails)
				&& ActionRequestDetails->IsValid())
			{
				if (FString ExtensionId;
					ActionRequestDetails->Get()->TryGetStringField(TEXT("extension_id"), ExtensionId))
				{
					TWeakObjectPtr<USageExtension> DruidsSageExtension =
						FActiveSageExtensions::Get().GetExtensionForId(ExtensionId);
					if (DruidsSageExtension.IsValid())
					{
						DruidsSageExtension.Get()->ExecuteAction(ActionRequestDetails->ToWeakPtr());
					}
				}
			}
		}
	}
}

TSharedPtr<FJsonObject> FSageExtensionDelegatorImplementation::OnQueryRequested(const TSharedPtr<FJsonObject>& QueryRequestObject)
{
	if (FString ExtensionId;
	QueryRequestObject.Get()->TryGetStringField(TEXT("extension_id"), ExtensionId))
	{
		TWeakObjectPtr<USageExtension> DruidsSageExtension =
			FActiveSageExtensions::Get().GetExtensionForId(ExtensionId);
		if (DruidsSageExtension.IsValid())
		{
			DruidsSageExtension.Get()->ExecuteQuery(QueryRequestObject.ToWeakPtr());

			if (const TSharedPtr<FJsonObject> *ResultsObject; QueryRequestObject->TryGetObjectField(TEXT("results"), ResultsObject))
			{
				return *ResultsObject;
			}
		}
	}

	return nullptr;
}
