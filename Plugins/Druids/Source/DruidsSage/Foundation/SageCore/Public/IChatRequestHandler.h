#pragma once

#include <CoreMinimal.h>

#include "IChatRequestHandler.generated.h"

class IDruidsSageChatItem;

UINTERFACE()
class SAGECORE_API UChatRequestHandler : public UInterface
{
	GENERATED_BODY()
};

class IChatRequestHandler
{
	GENERATED_BODY()

public:
    virtual bool IsNoActiveRequest() const = 0;
    virtual void StopAndCleanupRequest(TArray<TSharedPtr<IDruidsSageChatItem>> ChatItems) = 0;
    virtual void SetupAndSendRequest(TArray<TSharedPtr<IDruidsSageChatItem>> ChatItems,
                                     const TSharedPtr<IDruidsSageChatItem>& AssistantMessage, const FString& Context) = 0;
};