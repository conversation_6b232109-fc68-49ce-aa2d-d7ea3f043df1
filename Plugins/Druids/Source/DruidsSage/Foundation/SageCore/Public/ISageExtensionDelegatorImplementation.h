#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"

#include "ISageExtensionDelegator.generated.h"

UINTERFACE(BlueprintType, Blueprintable)
class SAGECORE_API USageExtensionDelegator : public UInterface
{
	GENERATED_BODY()
};

class SAGECORE_API ISageExtensionDelegator
{
	GENERATED_BODY()

public:
	virtual void OnActionApplied(const TSharedPtr<FJsonValue>& ActionDetails) = 0;
	virtual TSharedPtr<FJsonObject> OnQueryRequested(const TSharedPtr<FJsonObject>& QueryRequestObject) = 0;
};
