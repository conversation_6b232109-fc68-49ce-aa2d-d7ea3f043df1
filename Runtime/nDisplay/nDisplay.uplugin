{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "nDisplay", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "Description": "Support for synchronized clustered rendering using multiple PCs in mono or stereo", "Category": "Misc", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": true, "IsBetaVersion": false, "Installed": false, "SupportedTargetPlatforms": ["Win64", "Linux"], "Modules": [{"Name": "DisplayCluster", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux"]}, {"Name": "DisplayClusterFillDerivedDataCache", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux"]}, {"Name": "DisplayClusterEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux"]}, {"Name": "DisplayClusterProjection", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux"]}, {"Name": "DisplayClusterWarp", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux"]}, {"Name": "DisplayClusterShaders", "Type": "Runtime", "LoadingPhase": "PostConfigInit", "PlatformAllowList": ["Win64", "Linux"]}, {"Name": "DisplayClusterMessageInterception", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "DisplayClusterConfiguration", "Type": "Runtime", "LoadingPhase": "PostConfigInit", "PlatformAllowList": ["Win64", "Linux"]}, {"Name": "DisplayClusterOperator", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux"]}, {"Name": "DisplayClusterConfigurator", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux"]}, {"Name": "DisplayClusterLightCardEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux"]}, {"Name": "DisplayClusterLightCardEditorShaders", "Type": "Runtime", "LoadingPhase": "PostConfigInit", "PlatformAllowList": ["Win64", "Linux"]}, {"Name": "DisplayClusterScenePreview", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux"]}, {"Name": "DisplayClusterColorGrading", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux"]}, {"Name": "DisplayClusterDetails", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux"]}, {"Name": "DisplayClusterStageMonitoring", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "DisplayClusterRemoteControlInterceptor", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux"]}, {"Name": "DisplayClusterMedia", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux"]}, {"Name": "DisplayClusterMediaEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux"]}, {"Name": "DisplayClusterMultiUser", "Type": "UncookedOnly", "LoadingPhase": "PostEngineInit", "PlatformAllowList": ["Win64"]}, {"Name": "DisplayClusterTests", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux"]}, {"Name": "DisplayClusterMoviePipeline", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux"]}, {"Name": "DisplayClusterMoviePipelineEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux"]}, {"Name": "SharedMemoryMedia", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux"]}, {"Name": "SharedMemoryMediaEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux"]}, {"Name": "DisplayClusterReplication", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux"]}], "Plugins": [{"Name": "ColorCorrectRegions", "Enabled": true}, {"Name": "ColorGrading", "Enabled": true}, {"Name": "Composure", "Enabled": true}, {"Name": "ConcertSyncClient", "Enabled": true}, {"Name": "EnhancedInput", "Enabled": true}, {"Name": "MediaIOFramework", "Enabled": true}, {"Name": "nDisplayModularFeatures", "Enabled": true}, {"Name": "OpenCV", "Enabled": true}, {"Name": "ProceduralMeshComponent", "Enabled": true}, {"Name": "RemoteControlInterception", "Enabled": true}, {"Name": "MovieRenderPipeline", "Enabled": true}, {"Name": "OnlineSubsystem", "Enabled": true}, {"Name": "OnlineSubsystemUtils", "Enabled": true}]}